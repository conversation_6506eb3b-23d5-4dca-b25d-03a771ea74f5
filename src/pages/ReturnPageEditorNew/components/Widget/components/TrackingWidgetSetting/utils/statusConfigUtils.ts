import { Icon } from '@shopify/polaris';
import { ExternalMinor } from '@shopify/polaris-icons';
import React from 'react';
import { TrackingPage } from 'resources/tracking/useQueryTrackingPages';

import {
    StatusConfig,
    TrackingWidgetI18nKey,
    TrackingWidgetStatus,
} from '../types';

// 操作处理函数类型
interface ActionHandlers {
    handleCreateTrackingPage: () => void;
    handleEnableService: () => void;
    handleUpgrade: () => void;
}

// 生成带状态的选择选项
const getSelectOptions = (
    trackingPages: TrackingPage[],
    selectedTrackingPage: string,
    currentStatus: TrackingWidgetStatus,
    t: (key: string, defaultValue?: string) => string
) => {
    // 生成正常的页面选项
    const normalOptions = trackingPages.map(page => ({
        label: page.page_name,
        value: page.id,
        enabled: page.service_types?.includes('returns') || false,
        isDeleted: false,
    }));

    return normalOptions;
};

// 获取状态配置的工具函数
export const getStatusConfig = (
    status: TrackingWidgetStatus,
    t: (key: string, defaultValue?: string) => string,
    trackingPages: TrackingPage[],
    buttonText: string,
    selectedTrackingPage: string,
    actionHandlers: ActionHandlers
): StatusConfig => {
    const {
        handleCreateTrackingPage,
        handleEnableService,
        handleUpgrade,
    } = actionHandlers;

    // @ts-ignore
    const configs: Record<TrackingWidgetStatus, StatusConfig> = {
        [TrackingWidgetStatus.INITIAL]: {
            description: t(
                TrackingWidgetI18nKey.DESCRIPTION_INITIAL,
                'Customize a branded return tracking page, keeping customers updated on return shipments and reducing inquiries.'
            ),
            showLearnMore: true,
            primaryButton: {
                text: t(
                    TrackingWidgetI18nKey.BUTTON_CREATE,
                    'Create page in AfterShip Tracking'
                ),
                icon: React.createElement(Icon, { source: ExternalMinor }),
                handler: handleCreateTrackingPage,
            },
            showForm: false,
        },
        [TrackingWidgetStatus.NO_SUBSCRIPTION]: {
            description: t(
                TrackingWidgetI18nKey.DESCRIPTION_NO_SUBSCRIPTION,
                'Keep customers updated on their return shipments by connecting an AfterShip Tracking page where you enable the return tracking service.'
            ),
            showLearnMore: true,
            primaryButton: {
                text: t(
                    TrackingWidgetI18nKey.BUTTON_CREATE,
                    'Create page in AfterShip Tracking'
                ),
                icon: React.createElement(Icon, { source: ExternalMinor }),
                handler: handleCreateTrackingPage,
            },
            showForm: false,
        },
        [TrackingWidgetStatus.SERVICE_DISABLED]: {
            description: t(
                TrackingWidgetI18nKey.DESCRIPTION_SERVICE_DISABLED,
                'Keep customers updated on their return shipments by connecting an AfterShip Tracking page where you enable the return tracking service.'
            ),
            showLearnMore: true,
            primaryButton: {
                text: t(
                    TrackingWidgetI18nKey.BUTTON_ENABLE,
                    'Enable return tracking'
                ),
                icon: React.createElement(Icon, { source: ExternalMinor }),
                handler: handleEnableService,
            },
            showForm: false,
        },
        [TrackingWidgetStatus.REPLACE_PAGE]: {
            description: t(
                TrackingWidgetI18nKey.DESCRIPTION_REPLACE_PAGE,
                'Keep customers updated on their return shipments by connecting an AfterShip Tracking page where you enable the return tracking service.'
            ),
            showLearnMore: true,
            showForm: true,
            formConfig: {
                buttonTextEditable: true,
                buttonTextValue: buttonText,
                selectEditable: true,
                selectValue: selectedTrackingPage,
                selectOptions: getSelectOptions(
                    trackingPages,
                    selectedTrackingPage,
                    status,
                    t
                ),
                characterCount: `${buttonText.length}/48`,
            },
        },
        [TrackingWidgetStatus.PAGE_INVALID]: {
            description: '',
            showLearnMore: false,
            banner: {
                status: 'critical',
                content: t(
                    TrackingWidgetI18nKey.BANNER_PAGE_INVALID,
                    "This page doesn't support return tracking."
                ),
                actionText: t(
                    TrackingWidgetI18nKey.BANNER_ACTION_ENABLE,
                    'Enable now'
                ),
                actionHandler: handleEnableService,
            },
            showForm: true,
            formConfig: {
                buttonTextEditable: true,
                buttonTextValue: buttonText,
                selectEditable: true,
                selectValue: '',
                selectOptions: getSelectOptions(
                    trackingPages,
                    selectedTrackingPage,
                    status,
                    t
                ),
                characterCount: `${buttonText.length}/48`,
            },
        },
        [TrackingWidgetStatus.PAGE_DELETED_MULTIPLE]: {
            description: '',
            showLearnMore: false,
            banner: {
                status: 'critical',
                content: t(
                    TrackingWidgetI18nKey.BANNER_PAGE_DELETED_MULTIPLE,
                    'The connected page was deleted in AfterShip Tracking. Please select another page.'
                ),
            },
            showForm: true,
            formConfig: {
                buttonTextEditable: true,
                buttonTextValue: buttonText,
                selectEditable: true,
                selectValue: '',
                selectOptions: getSelectOptions(
                    trackingPages,
                    selectedTrackingPage,
                    status,
                    t
                ),
                characterCount: `${buttonText.length}/48`,
            },
        },
        [TrackingWidgetStatus.PAGE_DELETED_SINGLE]: {
            description: '',
            showLearnMore: false,
            banner: {
                status: 'critical',
                content: t(
                    TrackingWidgetI18nKey.BANNER_PAGE_DELETED_SINGLE,
                    'The connected page was deleted in AfterShip Tracking. Please create a new page.'
                ),
                actionHandler: handleCreateTrackingPage,
            },
            primaryButton: {
                text: t(
                    TrackingWidgetI18nKey.BUTTON_CREATE,
                    'Create page in AfterShip Tracking'
                ),
                icon: React.createElement(Icon, { source: ExternalMinor }),
                handler: handleCreateTrackingPage,
            },
            showForm: false,
        },
        [TrackingWidgetStatus.SUBSCRIPTION_EXPIRED]: {
            description: '',
            showLearnMore: false,
            banner: {
                status: 'critical',
                content: t(
                    TrackingWidgetI18nKey.BANNER_SUBSCRIPTION_EXPIRED,
                    'Your subscription to AfterShip Tracking Pro has expired.'
                ),
                actionText: t(
                    TrackingWidgetI18nKey.BANNER_ACTION_UPGRADE,
                    'Upgrade Tracking plan'
                ),
                actionHandler: handleUpgrade,
            },
            showForm: true,
            formConfig: {
                buttonTextEditable: true,
                buttonTextValue: buttonText,
                selectEditable: true,
                selectValue: '',
                selectOptions: [],
                characterCount: `${buttonText.length}/48`,
            },
        },
        [TrackingWidgetStatus.READY]: {
            description: '',
            showLearnMore: false,
            showForm: true,
            formConfig: {
                buttonTextEditable: true,
                buttonTextValue: buttonText,
                selectEditable: true,
                selectValue: selectedTrackingPage,
                selectOptions: getSelectOptions(
                    trackingPages,
                    selectedTrackingPage,
                    status,
                    t
                ),
                characterCount: `${buttonText.length}/48`,
            },
        },
    };

    return configs[status];
};
