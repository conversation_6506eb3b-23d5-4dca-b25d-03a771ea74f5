import {
    EventName,
    NavigateToType,
    UpdatePreviewDataType,
} from '@aftership/preview-kit/business/rc';
import { ReturnMethodSlug } from 'constants/ReturnMethodSlug';
import {
    Group,
    WidgetResourceCode,
} from 'pages/ReturnPageEditor/components/types/content';
import { ReturnStatusEnum } from 'pages/ReturnPageEditor/components/types/message';
import {
    usePreviewI18nResource,
    usePreviewMessageV2,
    usePreviewNavigateToPageV2,
} from 'pages/ReturnPageEditor/hooks/useMessage';
import { usePageEditorContext } from 'pages/ReturnPageEditor/PageEditorContext';
import { useCallback, useEffect, useState } from 'react';

import { TrackingWidgetStatus } from '../types';

interface UseTrackingWidgetConfigProps {
    elementId: string;
    setElementProp: (key: string, value: any) => void;
    initialButtonText?: string;
    initialSelectedTrackingPage?: string;
    currentStatus: TrackingWidgetStatus;
    trackingPages?: Array<{ id: string; service_types?: string[] }>;
    hasReturnsSubscription?: boolean;
}

interface UseTrackingWidgetConfigReturn {
    buttonText: string;
    selectedTrackingPage?: string;
    handleButtonTextChange: (value: string) => void;
    handleSelectPage: (pageId: string) => void;
    updateCurrentStatus?: (status: TrackingWidgetStatus) => void;
    syncConfigToShopper: (config: {
        button_text: string;
        tracking_page_id: string;
        enabled: boolean;
    }) => void;
}

// 配置相关逻辑的自定义hook
const useTrackingWidgetConfig = ({
    elementId,
    setElementProp,
    initialButtonText,
    initialSelectedTrackingPage,
}: UseTrackingWidgetConfigProps): UseTrackingWidgetConfigReturn => {
    // 内化状态管理
    const [buttonText, setButtonText] = useState(
        initialButtonText || 'Tracking your return'
    );
    const [selectedTrackingPage, setSelectedTrackingPage] = useState<
        string | undefined
    >(initialSelectedTrackingPage || '');

    // 添加消息发送钩子
    const {
        sendDebouncePreviewMessage: sendDebouncePreviewMessageV2,
        sendPreviewMessage,
    } = usePreviewMessageV2();

    // 添加文案中心钩子
    const { updateI18nResource } = usePreviewI18nResource();
    const { navigateToPage: navigateToPageV2 } = usePreviewNavigateToPageV2();
    const { updateGroupQuery } = usePageEditorContext();

    const updateReturnStatus = useCallback(
        (status: ReturnStatusEnum, return_method_slug: ReturnMethodSlug) => {
            sendPreviewMessage(EventName.UpdatePreviewData, {
                type: UpdatePreviewDataType.UpdateReturnDetailStatus,
                payload: { status, return_method_slug },
            });
        },
        [sendPreviewMessage]
    );

    useEffect(() => {
        // 跳转到 CheckRequestDetails 页面
        navigateToPageV2(NavigateToType.ReturnDetail);

        updateGroupQuery(Group.CheckRequestDetails);

        updateReturnStatus(
            ReturnStatusEnum.SHIPPED,
            ReturnMethodSlug.customerCourier
        );

        // AttaEditor 的事件监听器会自动处理 schema 同步
    }, [elementId, updateGroupQuery, updateReturnStatus, navigateToPageV2]);

    // 监听 buttonText 变化，直接更新文案
    useEffect(() => {
        updateI18nResource(WidgetResourceCode.TrackingWidgetText, buttonText);
    }, [buttonText, updateI18nResource]);

    const syncConfigToShopper = useCallback(
        (config: {
            button_text: string;
            tracking_page_id: string;
            enabled: boolean;
        }) => {
            // 直接使用现有的防抖消息发送函数
            sendDebouncePreviewMessageV2(EventName.UpdatePreviewData, {
                type: 'UpdateTrackingWidget' as UpdatePreviewDataType,
                payload: config,
            });
        },
        [sendDebouncePreviewMessageV2]
    );

    // 更新按钮文案处理函数 - 先更新 AttaEditor，让事件自动同步到 Formik
    const handleButtonTextChange = useCallback(
        (value: string) => {
            // 1️⃣ 首先更新 AttaEditor 中的元素属性，这会触发 ElementPropSet 事件
            setElementProp('button_text', value);

            // 2️⃣ 更新本地状态
            setButtonText(value);

            // 4️⃣ 更新国际化资源
            updateI18nResource(WidgetResourceCode.TrackingWidgetText, value);

            // 5️⃣ 同步到 Shopper 侧的配置
            syncConfigToShopper({
                button_text: value,
                tracking_page_id: selectedTrackingPage as string,
                enabled: true,
            });

            // 6️⃣ schema 更新由 AttaEditor 事件监听器自动处理，不需要手动调用
        },
        [
            setElementProp,
            selectedTrackingPage,
            syncConfigToShopper,
            updateI18nResource,
        ]
    );

    // 更新页面选择处理函数 - 先更新 AttaEditor，让事件自动同步到 Formik
    const handleSelectPage = useCallback(
        (pageId?: string) => {
            // 1️⃣ 首先更新 AttaEditor 中的元素属性，这会触发 ElementPropSet 事件
            setElementProp('tracking_page_id', pageId);

            // 2️⃣ 更新本地状态
            setSelectedTrackingPage(pageId);

            // 4️⃣ 同步到 Shopper 侧
            syncConfigToShopper({
                button_text: buttonText,
                tracking_page_id: pageId as string,
                enabled: true,
            });

            // 5️⃣ schema 更新由 AttaEditor 事件监听器自动处理，不需要手动调用
        },
        [setElementProp, buttonText, syncConfigToShopper]
    );

    // 初始化时同步 element props 到状态
    useEffect(() => {
        if (initialButtonText !== undefined) {
            setButtonText(initialButtonText);
        }
        if (initialSelectedTrackingPage !== undefined) {
            setSelectedTrackingPage(initialSelectedTrackingPage);
        }
    }, [initialButtonText, initialSelectedTrackingPage]);

    return {
        buttonText,
        selectedTrackingPage,
        handleButtonTextChange,
        handleSelectPage,

        syncConfigToShopper,
    };
};

export default useTrackingWidgetConfig;
