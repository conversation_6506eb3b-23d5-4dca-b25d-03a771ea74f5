import { SettingComponentProps } from '@aftership/atta-engine/editor';
import { useAuth } from '@aftership/automizely-product-auth';
import { Banner, Button, IconSource, Link, Spinner } from '@shopify/polaris';
import { useFormikContext } from 'formik';
import useIsAfterShipPayingUser from 'hooks/useIsAfterShipPayingUser';
import {
    PageEditorFormValues,
    WidgetResourceCode,
} from 'pages/ReturnPageEditor/components/types/content';
import { i18nCodeToFormikKey } from 'pages/ReturnPageEditor/utils';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import useQuerySubscriptions from 'resources/billing/useQuerySubscriptions';
import useQueryTrackingPages, {
    TrackingPage,
} from 'resources/tracking/useQueryTrackingPages';
import {
    openTrackingPageEditor,
    openTrackingPageManagement,
} from 'utils/tracking/getTrackingPageUrl';

import { DescriptionWithLink, FormSection, PreviewImage } from './components';
import useOriginalTrackingPageId from './hooks/useOriginalTrackingPageId';
import useTrackingWidgetConfig from './hooks/useTrackingWidgetConfig';
import useTrackingWidgetStatus from './hooks/useTrackingWidgetStatus';
import styles from './index.module.scss';
// 导入重构后的模块
import {
    FormLabels,
    TrackingWidgetI18nKey,
    TrackingWidgetProps,
    TrackingWidgetStatus,
} from './types';
import { getStatusConfig } from './utils/statusConfigUtils';

const TrackingWidgetSetting: React.FC<SettingComponentProps<
    TrackingWidgetProps
>> = props => {
    const { setElementProp, elementProps, elementId } = props;
    const { t } = useTranslation();
    const [{ organization }] = useAuth();
    // 从 Formik 获取 values 和 setFieldValue
    const { values, setFieldValue } = useFormikContext<PageEditorFormValues>();
    // 获取后端持久化的 schema 字符串
    const currentSchema = values.returnTrackingPageWidgetSchema;

    // 数据获取
    const {
        data: trackingPagesData,
        isLoading: isLoadingTrackingPages,
    } = useQueryTrackingPages({
        orgId: organization?.id,
    });

    const {
        data: subscriptionsData,
        isLoading: isLoadingSubscriptions,
    } = useQuerySubscriptions({
        productCode: 'returns',
        orgId: organization?.id,
        refetchOnWindowFocus: true,
    });

    // 获取后端保存的原始配置
    const originalTrackingPageId = useOriginalTrackingPageId();

    // 使用现有的tracking订阅检查hook
    const hasTrackingSubscription = useIsAfterShipPayingUser({
        refetchOnWindowFocus: true,
    });

    // 汇总所有 loading 状态
    const isLoading = useMemo(() => {
        return isLoadingTrackingPages || isLoadingSubscriptions;
    }, [isLoadingTrackingPages, isLoadingSubscriptions]);

    // 处理数据转换
    const trackingPages: TrackingPage[] = useMemo(() => {
        return trackingPagesData?.tracking_pages || [];
    }, [trackingPagesData]);

    // 检查是否有 returns 订阅功能
    const hasReturnsSubscription = useMemo(() => {
        const activeSubscriptions = subscriptionsData?.subscriptions?.filter(
            ({ active }) => active
        );

        return Boolean(activeSubscriptions && activeSubscriptions.length > 0);
    }, [subscriptionsData]);

    // 🔥 从 JSON schema 中解析当前值
    const parsedProps = useMemo(() => {
        if (!currentSchema) return {};
        try {
            const schema = JSON.parse(currentSchema);
            return schema?.$elements?.$props || {};
        } catch {
            return {};
        }
    }, [currentSchema]);

    // 使用配置管理hook - 重构后的版本（状态内化）
    const {
        buttonText,
        selectedTrackingPage,
        handleButtonTextChange,
        handleSelectPage,
        updateCurrentStatus,
    } = useTrackingWidgetConfig({
        elementId: String(elementId),
        setElementProp,
        initialButtonText: parsedProps.button_text || elementProps?.button_text,
        initialSelectedTrackingPage:
            parsedProps.tracking_page_id || elementProps?.tracking_page_id,
        currentStatus: TrackingWidgetStatus.READY, // 使用一个默认状态
        trackingPages,
        hasReturnsSubscription,
    });

    // 初始化时仅在“刚添加 widget”时执行
    useEffect(() => {
        const formikKey = i18nCodeToFormikKey(
            WidgetResourceCode.TrackingWidgetText
        );
        // 1. 初始化/重置表单字段
        setFieldValue(formikKey, buttonText);
    }, []);

    // 使用状态管理hook
    const { currentStatus } = useTrackingWidgetStatus({
        trackingPages,
        hasReturnsSubscription,
        hasTrackingSubscription,
        selectedTrackingPage: selectedTrackingPage as string,
        originalTrackingPageId: originalTrackingPageId,
        isLoadingTrackingPages,
    });

    // 同步状态到配置管理hook
    useEffect(() => {
        if (updateCurrentStatus) {
            updateCurrentStatus(currentStatus);
        }
    }, [currentStatus, updateCurrentStatus]);

    // 事件处理函数
    const handleCreateTrackingPage = () => {
        // create 按钮跳转到列表页面
        openTrackingPageManagement(false, true, organization?.id);
    };

    const handleEnableService = () => {
        // 当处于 SERVICE_DISABLED 状态时，说明用户只有一个 tracking page 且未启用 returns 服务
        // 直接跳转到该页面的编辑器页面
        if (trackingPages.length > 0) {
            openTrackingPageEditor(
                trackingPages[0].id,
                true,
                false,
                organization?.id
            );
        }
    };

    const handleUpgrade = () => {
        // 跳转到 tracking pages 然后自动会被重定向到 billing 页面
        openTrackingPageManagement(true, false, organization?.id);
    };

    const handleLearnMore = () => {
        const learnMoreUrl =
            'https://help.aftership.com/hc/en-us/articles/tracking-page';
        window.open(learnMoreUrl, '_blank', 'noopener,noreferrer');
    };

    // 处理启用页面
    const handleEnablePage = (pageId: string) => {
        // 跳转到 tracking page 编辑器页面
        openTrackingPageEditor(pageId, true, false, organization?.id);
    };

    // 处理编辑页面
    const handleEditPages = () => {
        // 如果有选中的页面，跳转到该页面的编辑器
        if (selectedTrackingPage) {
            openTrackingPageEditor(
                selectedTrackingPage,
                true,
                false,
                organization?.id
            );
        } else {
            // 如果没有选中页面，跳转到管理页面
            openTrackingPageManagement(false, true, organization?.id);
        }
    };

    // 获取状态配置
    const statusConfig = getStatusConfig(
        currentStatus,
        t,
        trackingPages,
        buttonText,
        selectedTrackingPage as string,
        {
            handleCreateTrackingPage,
            handleEnableService,
            handleUpgrade,
        }
    );

    if (!elementProps) {
        return null;
    }

    // 如果任何异步操作正在加载中，显示加载状态
    if (isLoading) {
        return (
            <div className={styles.container}>
                <div className={styles.loadingContainer}>
                    <div className={styles.loadingContent}>
                        <Spinner accessibilityLabel="加载中..." size="large" />
                    </div>
                </div>
            </div>
        );
    }

    // 表单标签
    const formLabels: FormLabels = {
        buttonTextLabel: t(
            TrackingWidgetI18nKey.BUTTON_TEXT_LABEL,
            'Button text'
        ),
        buttonTextPlaceholder: t(
            TrackingWidgetI18nKey.BUTTON_TEXT_PLACEHOLDER,
            'Track your return'
        ),
        selectLabel: t(
            TrackingWidgetI18nKey.SELECT_PAGE_LABEL,
            'Tracking page'
        ),
        selectPlaceholder: t(
            TrackingWidgetI18nKey.SELECT_PLACEHOLDER,
            'Select...'
        ),
    };

    return (
        <div className={styles.container}>
            <div className={styles.content}>
                <h3 className={styles.title}>
                    {t(TrackingWidgetI18nKey.TITLE, 'Return tracking page')}
                </h3>
                <PreviewImage />

                {/* Banner 组件 */}
                {statusConfig.banner && (
                    <Banner status={statusConfig.banner.status}>
                        {statusConfig.banner.content}
                        {statusConfig.banner.actionText &&
                            statusConfig.banner.actionHandler && (
                                <span className={styles.enableNowLink}>
                                    &nbsp;
                                    <Link
                                        monochrome={false}
                                        onClick={
                                            statusConfig.banner.actionHandler
                                        }
                                    >
                                        {statusConfig.banner.actionText}
                                    </Link>
                                </span>
                            )}
                    </Banner>
                )}

                {/* 描述文本 */}
                {statusConfig.description && (
                    <DescriptionWithLink
                        description={statusConfig.description}
                        showLearnMore={statusConfig.showLearnMore}
                        onLearnMore={handleLearnMore}
                        learnMoreText={t(
                            TrackingWidgetI18nKey.LEARN_MORE,
                            'Learn more'
                        )}
                    />
                )}

                {/* 主要操作按钮 */}
                {statusConfig.primaryButton && (
                    <Button
                        fullWidth
                        onClick={statusConfig.primaryButton?.handler}
                        icon={statusConfig.primaryButton?.icon as IconSource}
                    >
                        {statusConfig.primaryButton.text}
                    </Button>
                )}

                {/* 表单部分 */}
                {statusConfig.showForm && (
                    <FormSection
                        config={statusConfig.formConfig}
                        onButtonTextChange={handleButtonTextChange}
                        onSelectPage={handleSelectPage}
                        onEnablePage={handleEnablePage}
                        onEditPages={handleEditPages}
                        labels={formLabels}
                    />
                )}
            </div>
        </div>
    );
};

export default TrackingWidgetSetting;
