/* eslint-disable max-lines */
/* eslint-disable spellcheck/spell-checker */

import {
    DescriptionList,
    Heading,
    Link,
    Stack,
    Tooltip,
} from '@shopify/polaris';
import { FlagMajor } from '@shopify/polaris-icons';
import { DATE_END } from 'constants/Date';
import { isNil, omit, omitBy, startCase } from 'lodash';
import moment from 'moment-timezone';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ShipmentTag, TrackingDetail } from 'types/aftership';
import { additionalFieldsWithLabel } from 'utils/additionalFields';
import { to3rdPartyTime } from 'utils/day';

import style from '../../ShipmentDetailsSheet.module.scss';
import CopyElem from '../CopyElem/CopyElem';
import EddDetail from './components/EddDetail';
import { PostalCodeContent } from './components/PostalCodeContent';
import { ShipmentCarrier } from './components/shipmentCarrier';
import UnderlineTooltip from './components/ShipmentsUnderlineTooltip';

interface OrderSectionProps {
    trackingDetail?: TrackingDetail;
    trackingRefetch: VoidFunction;
    allTags?: Record<string, ShipmentTag>;
}

function OrderSection({
    trackingDetail,
    trackingRefetch,
    allTags,
}: OrderSectionProps) {
    const { t } = useTranslation();

    const orders = trackingDetail?.orders?.[0];
    const serviceTypeName =
        trackingDetail?.userServiceType?.name ||
        trackingDetail?.courierServiceType?.name;
    const shipmentTags = trackingDetail?.shipmentTags;
    const destinationAddress = trackingDetail?.destinationAddress?.location;
    const shippingAddress = destinationAddress;
    const orderId = orders?.storeOrderId;
    const orderNumber = orders?.storeOrderNumber;
    const additionalFields = omit(trackingDetail?.additionalFields || {}, [
        'originCountry',
        'destinationCountry',
        'postalCode',
    ]);
    const additionalList = Object.entries(omitBy(additionalFields, isNil)).map(
        ([key, value]) => ({
            term: additionalFieldsWithLabel(key),
            description: value,
        })
    );
    const destinationTimezone =
        trackingDetail?.destinationAddress?.standardizedTimezone;

    const tags = useMemo(
        () =>
            shipmentTags?.map(({ id }) => allTags?.[id]).filter(Boolean) || [],
        [shipmentTags, allTags]
    );

    const getFormattedDate = useCallback(
        (
            dateString: string | null | undefined,
            type: 'org' | '3rdParty' | 'checkpoint',
            timezone?: string
        ) => {
            if (!dateString) {
                return '-';
            }
            if (type === 'org') {
                const dateTime = to3rdPartyTime(dateString, {
                    useOrgTimezone: true,
                });

                return (
                    <Tooltip content={dateTime.defaultStrV2}>
                        <span>{dateTime.dateStr}</span>
                    </Tooltip>
                );
            }
            if (type === '3rdParty') {
                return (
                    <Tooltip
                        content={t('rma.shipment.local_time', 'Local time')}
                    >
                        <span
                            style={{
                                borderBottom: '1px dashed #6d7175',
                            }}
                        >
                            {to3rdPartyTime(dateString, { timezone }).dateStr}
                        </span>
                    </Tooltip>
                );
            }
            if (type === 'checkpoint') {
                return (
                    <Tooltip
                        content={t(
                            'rma.shipment.local_time_checkpoint',
                            'Local time'
                        )}
                    >
                        <span
                            style={{
                                borderBottom: '1px dashed #6d7175',
                            }}
                        >
                            {to3rdPartyTime(dateString).dateStr}
                        </span>
                    </Tooltip>
                );
            }

            return <></>;
        },
        [t]
    );

    const orderDescription = useMemo(() => {
        const orderDescription = [
            {
                term: t('rma.shipment.order.id', 'Order ID') as string,
                description: orderId ? (
                    <CopyElem
                        size="16px"
                        value={orderId}
                        message={t(
                            'rma.shipment.order.id_copied',
                            'Order ID copied to clipboard'
                        )}
                    >
                        {orderId}
                    </CopyElem>
                ) : (
                    '-'
                ),
            },
            {
                term: t('rma.shipment.order.number', 'Order number') as string,
                description: orderNumber ? (
                    <CopyElem
                        size="16px"
                        value={orderNumber}
                        message={t(
                            'rma.shipment.order.number_copied',
                            'Order number copied to clipboard'
                        )}
                    >
                        {orderNumber}
                    </CopyElem>
                ) : (
                    '-'
                ),
            },
            {
                term: (
                    <UnderlineTooltip
                        text={t('rma.shipment.title', 'Shipment title')}
                        content={t(
                            'rma.shipment.title.tooltip',
                            'Displayed as the tracking number by default, but you can customize the title'
                        )}
                    />
                ),
                description: trackingDetail?.title ? (
                    <CopyElem
                        size="16px"
                        value={trackingDetail?.title}
                        message={t(
                            'rma.shipment.title.copied',
                            'Order title copied to clipboard'
                        )}
                    >
                        {trackingDetail?.title}
                    </CopyElem>
                ) : (
                    '-'
                ),
            },
            {
                term: t(
                    'rma.shipment.tracking_number',
                    'Tracking number'
                ) as string,
                description: trackingDetail?.trackingNumber ? (
                    <CopyElem
                        size="16px"
                        value={trackingDetail?.trackingNumber}
                        message={t(
                            'rma.shipment.tracking_number.copied',
                            'Tracking number copied to clipboard'
                        )}
                    >
                        {trackingDetail?.trackingNumber}
                    </CopyElem>
                ) : (
                    '-'
                ),
            },
            // Add last mile tracking number if exists
            ...(trackingDetail?.multipleFulfilledCarriers?.lastMile
                ?.trackingNumber
                ? [
                      {
                          term: t(
                              'rma.shipment.last_mile_tracking_number',
                              'Last mile tracking number'
                          ) as string,
                          description: trackingDetail?.multipleFulfilledCarriers
                              ?.lastMile?.trackingNumber ? (
                              <CopyElem
                                  size="16px"
                                  value={
                                      trackingDetail?.multipleFulfilledCarriers
                                          ?.lastMile?.trackingNumber
                                  }
                                  message={t(
                                      'rma.shipment.tracking_number.copied',
                                      'Tracking number copied to clipboard'
                                  )}
                              >
                                  {
                                      trackingDetail?.multipleFulfilledCarriers
                                          ?.lastMile?.trackingNumber
                                  }
                              </CopyElem>
                          ) : (
                              '-'
                          ),
                      },
                  ]
                : []),
            {
                term: t('rma.shipment.carrier', 'Carrier') as string,
                description: trackingDetail?.slug ? (
                    <ShipmentCarrier trackingDetail={trackingDetail} />
                ) : (
                    '-'
                ),
            },
            {
                term: t(
                    'rma.shipment.service_type',
                    'Carrier service type'
                ) as string,
                description: serviceTypeName || '-',
            },
            // Add last mile carrier if exists
            ...(trackingDetail?.multipleFulfilledCarriers?.lastMile
                ?.fulfilledCarrierName
                ? [
                      {
                          term: t(
                              'rma.shipment.last_mile_carrier',
                              'Last mile carrier'
                          ) as string,
                          description:
                              trackingDetail?.multipleFulfilledCarriers
                                  ?.lastMile?.fulfilledCarrierName,
                      },
                  ]
                : []),
            {
                term: t('rma.shipment.source', 'Source / created at') as string,
                description: (
                    <span>
                        {trackingDetail?.source
                            ? startCase(trackingDetail?.source)
                            : '-'}{' '}
                        /{' '}
                        {getFormattedDate(trackingDetail?.createdAt, 'org') ||
                            '-'}
                    </span>
                ),
            },
            {
                term: t('rma.shipment.store', 'Store') as string,
                description: (
                    <span>{trackingDetail?.orders?.[0]?.storeId || '-'}</span>
                ),
            },
            // Add Shopify order tags if source is shopify
            ...[
                trackingDetail?.source === 'shopify'
                    ? {
                          term: (
                              <UnderlineTooltip
                                  text={t(
                                      'rma.shipment.shopify_order_tags',
                                      'Shopify order tags'
                                  )}
                                  content={t(
                                      'rma.shipment.shopify_order_tags.tooltip',
                                      'It helps you filter your order based on the Order Tags'
                                  )}
                              />
                          ),
                          description: orders?.tags?.length ? (
                              <Stack spacing="tight" distribution="trailing">
                                  {orders.tags.map((tag: string) => (
                                      <div
                                          key={tag}
                                          style={{
                                              height: '28px',
                                              lineHeight: '28px',
                                              padding: '0px 8px',
                                              background: '#e4e5e7',
                                              borderRadius: '4px',
                                          }}
                                      >
                                          {tag}
                                      </div>
                                  ))}
                              </Stack>
                          ) : (
                              '-'
                          ),
                      }
                    : undefined,
            ].filter(Boolean),
        ];

        // Add language if exists
        if (trackingDetail?.language) {
            const lang = trackingDetail?.language;
            orderDescription.push({
                term: t('rma.shipment.language', 'Language') as string,
                description: lang || '-', // You may want to add proper language mapping here
            });
        }

        // Add special store handling for magento
        if (
            ['magento-1', 'magento-2'].includes(trackingDetail?.source || '') &&
            orders?.storeId
        ) {
            // Find and update the Store field
            const storeIndex = orderDescription.findIndex(
                item =>
                    item?.term === (t('rma.shipment.store', 'Store') as string)
            );
            if (storeIndex !== -1) {
                orderDescription[storeIndex] = {
                    term: t('rma.shipment.store', 'Store') as string,
                    description: orders?.storeId?.includes('|')
                        ? orders?.storeId.split('|')[1]
                        : orders?.storeId,
                };
            }
        }

        // Add order path if exists
        if (orders?.storeOrderIdPath) {
            orderDescription.push({
                term: t('rma.shipment.order_path', 'Order Path') as string,
                description: orders?.storeOrderIdPath,
            });
        }

        return orderDescription.filter(Boolean);
    }, [
        t,
        orderId,
        orderNumber,
        trackingDetail,
        serviceTypeName,
        orders,
        getFormattedDate,
    ]);

    const deliveryTime = moment(
        trackingDetail?.shipmentDeliveryDate?.slice(0, 19)
    );
    const dateStr = deliveryTime.format(DATE_END);
    const timeStr = deliveryTime.format('[at] hh:mm a');
    const showedDeliveryTime = !timeStr.includes('11:59')
        ? dateStr + ' ' + timeStr
        : dateStr;

    const deliverDateSpan = (
        <span
            style={{
                borderBottom: trackingDetail?.shipmentDeliveryDate
                    ? '1px dashed #6d7175'
                    : 'none',
                marginRight: '4px',
            }}
        >
            {trackingDetail?.shipmentDeliveryDate
                ? moment(
                      trackingDetail?.shipmentDeliveryDate?.slice(0, 19)
                  ).format(DATE_END)
                : '-'}
        </span>
    );

    const deliverDate = (
        <Tooltip
            content={
                <div>
                    <div>{`${showedDeliveryTime}`}</div>
                    <div>
                        (
                        {t(
                            'rma.shipment.local_time_customer',
                            'Local time of customer'
                        )}
                        )
                    </div>
                </div>
            }
        >
            {deliverDateSpan}
        </Tooltip>
    );

    const shipmentWeight = trackingDetail?.shipmentWeight;

    return (
        <>
            <div className={style.orderSection}>
                <div style={{ padding: '20px 20px 12px' }}>
                    <div style={{ marginBottom: '16px' }}>
                        <Stack distribution="equalSpacing">
                            {orders?.storeOrderId ? (
                                <div className={style.orderDetailsTitle}>
                                    <Heading>
                                        {t(
                                            'rma.shipment.section.order',
                                            'Order'
                                        )}
                                    </Heading>
                                </div>
                            ) : (
                                <Heading>
                                    {t(
                                        'rma.shipment.shipment_details',
                                        'Shipment details'
                                    )}
                                </Heading>
                            )}
                        </Stack>
                    </div>
                    <DescriptionList items={orderDescription as any} />
                </div>
                <div className={style.dividedLine} />
                <div style={{ padding: '12px 20px 12px' }}>
                    <DescriptionList
                        items={[
                            {
                                term: t(
                                    'rma.shipment.shipping_address',
                                    'Shipping address'
                                ) as string,
                                description: shippingAddress || '-',
                            },
                            {
                                term: t(
                                    'rma.shipment.weight',
                                    'Weight'
                                ) as string,
                                description: shipmentWeight?.value
                                    ? `${shipmentWeight?.value} ${shipmentWeight?.unit}`
                                    : '-',
                            },
                            {
                                term: t(
                                    'rma.shipment.shipping_method',
                                    'Shipping method'
                                ) as string,
                                description:
                                    trackingDetail?.shippingMethod || '-',
                            },
                            {
                                term: t(
                                    'rma.shipment.postal_code',
                                    'Postal code'
                                ) as string,
                                description: trackingDetail ? (
                                    <PostalCodeContent
                                        trackingDetail={trackingDetail}
                                    />
                                ) : (
                                    '-'
                                ),
                            },
                            ...additionalList,
                        ]}
                    />
                </div>
                <div className={style.dividedLine} />
                <EddDetail trackingDetail={trackingDetail} />
                <div className={style.dividedLine} />
                <div style={{ padding: '12px 20px 12px' }}>
                    <DescriptionList
                        items={[
                            {
                                term: t(
                                    'rma.shipment.delivered_date',
                                    'Delivered date'
                                ) as string,
                                description:
                                    trackingDetail?.userMarkedReason ===
                                    'DELIVERED' ? (
                                        <Link>
                                            <div>
                                                <span>
                                                    {trackingDetail?.shipmentDeliveryDate
                                                        ? deliverDate
                                                        : deliverDateSpan}
                                                </span>
                                                <Tooltip
                                                    content={t(
                                                        'rma.shipment.marked_as_delivered',
                                                        'Marked as delivered'
                                                    )}
                                                >
                                                    <span
                                                        style={{
                                                            verticalAlign:
                                                                'middle',
                                                        }}
                                                    >
                                                        <FlagMajor
                                                            fill="#2c6ecb"
                                                            width={16}
                                                            height={16}
                                                        />
                                                    </span>
                                                </Tooltip>
                                            </div>
                                        </Link>
                                    ) : (
                                        getFormattedDate(
                                            trackingDetail?.shipmentDeliveryDate,
                                            '3rdParty'
                                        )
                                    ),
                            },
                            {
                                term: t(
                                    'rma.shipment.pickup_date_short',
                                    'Pickup date'
                                ) as string,
                                description: getFormattedDate(
                                    trackingDetail?.shipmentPickupDate,
                                    'checkpoint'
                                ),
                            },
                        ]}
                    />
                </div>
            </div>
        </>
    );
}

export default OrderSection;
